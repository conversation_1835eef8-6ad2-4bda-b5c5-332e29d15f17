# widgets.yaml

- "Gestión y Red":
    - adguard:
        type: adguard
        url: http://tankeguard.duckdns.org
        username: TU_USUARIO_ADGUARD # <--- CAMBIA ESTO
        password: TU_CLAVE_ADGUARD   # <--- <PERSON><PERSON><PERSON> ESTO

- "Descargas":
    - qbittorrent:
        type: qbittorrent
        url: http://tanketorrent.duckdns.org
        username: TU_USUARIO_QBIT # <--- CAMBIA ESTO
        password: TU_CLAVE_QBIT   # <--- CAMBIA ESTO

- "Automatización Multimedia":
    - sonarr:
        type: sonarr
        url: http://tankesonarr.duckdns.org
        key: {{HOMEPAGE_VAR_SONARR_API_KEY}} # <-- Se cargará desde los secrets
    - radarr:
        type: radarr
        url: http://tankeradarr.duckdns.org
        key: {{HOMEPAGE_VAR_RADARR_API_KEY}} # <-- Se cargará desde los secrets

- "Centro Multimedia":
    - jellyfin:
        type: jellyfin
        url: http://tankeflix.duckdns.org
        key: {{HOMEPAGE_VAR_JELLYFIN_API_KEY}} # <-- Se cargará desde los secrets

- "Recursos del Sistema":
    - cpu:
        type: cpu
    - memory:
        type: memory
    - storage_d:
        type: storage
        path: /mnt/d
        label: "Disco D:"
    - storage_e:
        type: storage
        path: /mnt/e
        label: "Disco E:"
    - storage_f:
        type: storage
        path: /mnt/f
        label: "Disco F:"```

#### **4. `bookmarks.yaml`**
*Este archivo crea las barras de búsqueda en la parte superior de tu panel.*

```yaml
# bookmarks.yaml

- "Búsqueda":
    - DuckDuckGo:
        abbreviation: DDG
        url: https://duckduckgo.com/?q={}

- "Multimedia":
    - Jellyfin:
        abbreviation: JF
        url: http://tankeflix.duckdns.org/web/index.html#!/search?q={}
    - Sonarr:
        abbreviation: SN
        url: http://tankesonarr.duckdns.org/add/new?term={}
    - Radarr:
        abbreviation: RD
        url: http://tankeradarr.duckdns.org/add/new?term={}