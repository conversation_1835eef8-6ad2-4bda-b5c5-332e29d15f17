# services.yaml

- "Gestión y Red":
    - Portainer:
        href: http://host.docker.internal:9000 # Acceso directo a Portainer
        description: Gestión de Contenedores Docker
        icon: portainer.png
        widget:
          type: docker
          host: /var/run/docker.sock

    - AdGuard Home:
        href: http://tankeguard.duckdns.org
        description: Bloqueador de Anuncios y DNS
        icon: adguard-home.png

    - Watchtower:
        href: https://containrrr.dev/watchtower/
        description: Actualizador automático de contenedores
        icon: watchtower.png

- "Descargas":
    - qBittorrent:
        href: http://tanketorrent.duckdns.org
        description: Cliente BitTorrent
        icon: qbittorrent.png

    - Jackett:
        href: http://tankejackett.duckdns.org
        description: Proxy de Indexers
        icon: jackett.png

- "Automatización Multimedia":
    - Sonarr:
        href: http://tankesonarr.duckdns.org
        description: Gestión de Series
        icon: sonarr.png

    - Radarr:
        href: http://tankeradarr.duckdns.org
        description: Gestión de Películas
        icon: radarr.png

- "Centro Multimedia":
    - Jellyfin:
        href: http://tankeflix.duckdns.org
        description: Servidor de Medios
        icon: jellyfin.png
        
    - Jellyseerr:
        href: http://tankejellyseerr.duckdns.org
        description: Solicitudes de Contenido
        icon: jellyseerr.png